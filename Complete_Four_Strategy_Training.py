#!/usr/bin/env python3
"""
Complete Trading Bot Training System for All Four Strategies
Training on Strategies 1, 2, 3, and 4
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
import joblib
import os
from datetime import datetime

print("🚀 COMPLETE FOUR STRATEGY TRAINING SYSTEM")
print("Training on ALL Strategies: S1, S2, S3, and S4")
print("=" * 70)

# Strategy Configuration
strategies = {
    'S1': 'output_signals_S1.csv',
    'S2': 'output_signals_S2.csv', 
    'S3': 'output_signals_S3.csv',
    'S4': 'output_signals_S4.csv'
}

strategy_names = {
    'S1': 'Breakout with Volume',
    'S2': 'Order Block Strategy',
    'S3': 'Support/Resistance Rejection',
    'S4': 'Trendline Break with Rejection'
}

print("\n📊 Loading Strategy Data...")
print("-" * 50)

all_data = []
strategy_stats = {}

for strategy, filename in strategies.items():
    print(f"Loading {filename}...")
    
    try:
        df = pd.read_csv(filename)
        print(f"✅ Loaded {strategy}: {len(df)} rows")
        
        # Add strategy identifier
        df['strategy_source'] = strategy
        
        # Get the correct signal column name
        signal_col = f'strategy{strategy[1:]}_signal'
        
        # Fill NaN values in strategy signal column
        if signal_col in df.columns:
            df[signal_col] = df[signal_col].fillna(0)
        else:
            print(f"⚠️  Warning: {signal_col} not found in {filename}")
            continue
        
        # Count signals
        signal_counts = df[signal_col].value_counts()
        buy_signals = signal_counts.get(1, 0)
        sell_signals = signal_counts.get(-1, 0)
        hold_signals = signal_counts.get(0, 0)
        
        strategy_stats[strategy] = {
            'total_rows': len(df),
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'hold_signals': hold_signals,
            'actionable_signals': buy_signals + sell_signals
        }
        
        print(f"   📈 BUY signals: {buy_signals}")
        print(f"   📉 SELL signals: {sell_signals}")
        print(f"   ⏸️  HOLD signals: {hold_signals}")
        print(f"   🎯 Actionable signals: {buy_signals + sell_signals}")
        
        all_data.append(df)
        
    except FileNotFoundError:
        print(f"❌ Error: {filename} not found!")
        continue
    except Exception as e:
        print(f"❌ Error loading {filename}: {str(e)}")
        continue

if not all_data:
    print("❌ No data loaded! Please check your CSV files.")
    exit()

# Combine all data
print(f"\n🔄 Combining all strategy data...")
data = pd.concat(all_data, ignore_index=True)
print(f"✅ Combined dataset: {len(data)} rows")

# Create labels based on strategy signals
print("\n🏷️  Creating trading labels...")
def create_label(row):
    strategy = row['strategy_source']
    signal_col = f'strategy{strategy[1:]}_signal'
    
    if signal_col in row:
        signal = row[signal_col]
        if signal == 1:
            return 'BUY'
        elif signal == -1:
            return 'SELL'
        else:
            return 'HOLD'
    return 'HOLD'

data['trading_signal'] = data.apply(create_label, axis=1)

# Display label distribution
label_dist = data['trading_signal'].value_counts()
print(f"✅ Overall label distribution:")
for label, count in label_dist.items():
    print(f"   {label}: {count} ({count/len(data)*100:.1f}%)")

# Feature Engineering
print(f"\n🔧 Feature Engineering...")

# Select relevant features (excluding signal columns and metadata)
exclude_cols = ['time', 'trading_signal', 'strategy_source'] + [f'strategy{i}_signal' for i in range(1, 5)]
feature_cols = [col for col in data.columns if col not in exclude_cols]

print(f"✅ Selected {len(feature_cols)} features:")
print(f"   {feature_cols[:10]}..." if len(feature_cols) > 10 else f"   {feature_cols}")

# Prepare features and labels
X = data[feature_cols].fillna(0)
y = data['trading_signal']

# Handle categorical variables
print(f"\n🔧 Handling categorical variables...")
categorical_cols = X.select_dtypes(include=['object']).columns.tolist()
if categorical_cols:
    print(f"   Found categorical columns: {categorical_cols}")
    for col in categorical_cols:
        if col == 'direction':
            # Encode direction: bullish=1, bearish=0
            X[col] = X[col].map({'bullish': 1, 'bearish': 0}).fillna(0)
            print(f"   ✅ Encoded {col}: bullish=1, bearish=0")
        else:
            # For other categorical columns, use label encoding
            col_encoder = LabelEncoder()
            X[col] = col_encoder.fit_transform(X[col].astype(str))
            print(f"   ✅ Encoded {col} with LabelEncoder")

print(f"\n📊 Dataset Summary:")
print(f"   Features: {X.shape[1]}")
print(f"   Total samples: {X.shape[0]}")
print(f"   BUY samples: {(y == 'BUY').sum()}")
print(f"   SELL samples: {(y == 'SELL').sum()}")
print(f"   HOLD samples: {(y == 'HOLD').sum()}")

# Filter actionable signals for training
actionable_mask = y.isin(['BUY', 'SELL'])
X_actionable = X[actionable_mask]
y_actionable = y[actionable_mask]

print(f"\n🎯 Actionable signals for training:")
print(f"   Samples: {len(X_actionable)}")
print(f"   BUY: {(y_actionable == 'BUY').sum()}")
print(f"   SELL: {(y_actionable == 'SELL').sum()}")

if len(X_actionable) < 100:
    print("⚠️  Warning: Very few actionable signals for training!")

# Encode labels
print(f"\n🔤 Encoding labels...")
le = LabelEncoder()
y_encoded = le.fit_transform(y_actionable)
print(f"✅ Label encoding: {dict(zip(le.classes_, range(len(le.classes_))))}")

# Scale features
print(f"\n📏 Scaling features...")
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X_actionable)
print(f"✅ Features scaled")

# Train/test split
print(f"\n✂️  Splitting data...")
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y_encoded, 
    test_size=0.2, 
    random_state=42, 
    stratify=y_encoded
)

print(f"✅ Training set: {len(X_train)} samples")
print(f"✅ Test set: {len(X_test)} samples")

# Model Training
print(f"\n🤖 Training Advanced Models...")
print("-" * 50)

models = {
    'RandomForest': RandomForestClassifier(
        n_estimators=200,
        max_depth=15,
        min_samples_split=5,
        min_samples_leaf=2,
        random_state=42,
        n_jobs=-1
    ),
    'GradientBoosting': GradientBoostingClassifier(
        n_estimators=150,
        learning_rate=0.1,
        max_depth=8,
        random_state=42
    ),
    'LogisticRegression': LogisticRegression(
        random_state=42,
        max_iter=1000,
        C=1.0
    )
}

trained_models = {}
results = {}

for name, model in models.items():
    print(f"\n🔄 Training {name}...")
    
    # Train
    model.fit(X_train, y_train)
    
    # Evaluate
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    # Cross-validation
    cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
    
    print(f"   ✅ Accuracy: {accuracy:.4f}")
    print(f"   ✅ CV Score: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
    
    trained_models[name] = model
    results[name] = {
        'accuracy': accuracy,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'predictions': y_pred
    }

# Create ensemble
print(f"\n🎭 Creating ensemble model...")
ensemble = VotingClassifier(
    estimators=[(name, model) for name, model in trained_models.items()],
    voting='soft'
)

ensemble.fit(X_train, y_train)
ensemble_pred = ensemble.predict(X_test)
ensemble_acc = accuracy_score(y_test, ensemble_pred)
ensemble_cv = cross_val_score(ensemble, X_train, y_train, cv=5, scoring='accuracy')

print(f"✅ Ensemble Accuracy: {ensemble_acc:.4f}")
print(f"✅ Ensemble CV Score: {ensemble_cv.mean():.4f} ± {ensemble_cv.std():.4f}")

trained_models['Ensemble'] = ensemble
results['Ensemble'] = {
    'accuracy': ensemble_acc,
    'cv_mean': ensemble_cv.mean(),
    'cv_std': ensemble_cv.std(),
    'predictions': ensemble_pred
}

# Find best model
best_name = max(results.keys(), key=lambda x: results[x]['accuracy'])
best_model = trained_models[best_name]
best_acc = results[best_name]['accuracy']
best_cv = results[best_name]['cv_mean']

print(f"\n🏆 Best Model: {best_name}")
print(f"📊 Best Accuracy: {best_acc:.4f}")
print(f"🎯 Best CV Score: {best_cv:.4f}")

# Detailed evaluation
print(f"\n📋 Classification Report ({best_name}):")
best_pred = results[best_name]['predictions']
print(classification_report(y_test, best_pred, target_names=le.classes_))

print(f"\n🔍 Confusion Matrix:")
cm = confusion_matrix(y_test, best_pred)
print(cm)

# Save models
print(f"\n💾 Saving models...")
os.makedirs('trained_models', exist_ok=True)

# Save all individual models
for name, model in trained_models.items():
    model_filename = f'trained_models/{name.lower()}_four_strategy_model.pkl'
    joblib.dump(model, model_filename)
    print(f"✅ Saved {name} to {model_filename}")

# Save preprocessing components
joblib.dump(scaler, 'trained_models/four_strategy_scaler.pkl')
joblib.dump(le, 'trained_models/four_strategy_label_encoder.pkl')
joblib.dump(feature_cols, 'trained_models/four_strategy_features.pkl')

print(f"✅ Saved preprocessing components")

# Save comprehensive summary
summary = {
    'training_timestamp': datetime.now().isoformat(),
    'strategies_used': list(strategies.keys()),
    'strategy_names': strategy_names,
    'strategy_stats': strategy_stats,
    'best_model': best_name,
    'best_accuracy': best_acc,
    'best_cv_score': best_cv,
    'model_results': {name: {'accuracy': res['accuracy'], 'cv_score': res['cv_mean']}
                     for name, res in results.items()},
    'feature_count': len(feature_cols),
    'total_samples': len(data),
    'actionable_samples': len(X_actionable),
    'training_samples': len(X_train),
    'test_samples': len(X_test),
    'label_distribution': label_dist.to_dict()
}

joblib.dump(summary, 'trained_models/four_strategy_training_summary.pkl')

# Calculate total signals per strategy
print(f"\n📊 STRATEGY PERFORMANCE SUMMARY")
print("=" * 70)

total_buy_signals = 0
total_sell_signals = 0
total_actionable_signals = 0

for strategy, stats in strategy_stats.items():
    strategy_name = strategy_names[strategy]
    print(f"\n🎯 {strategy} - {strategy_name}:")
    print(f"   📈 BUY signals: {stats['buy_signals']}")
    print(f"   📉 SELL signals: {stats['sell_signals']}")
    print(f"   ⏸️  HOLD signals: {stats['hold_signals']}")
    print(f"   🎯 Total actionable: {stats['actionable_signals']}")
    print(f"   📊 Win rate potential: {stats['actionable_signals']/stats['total_rows']*100:.2f}%")

    total_buy_signals += stats['buy_signals']
    total_sell_signals += stats['sell_signals']
    total_actionable_signals += stats['actionable_signals']

print(f"\n🏆 OVERALL SUMMARY:")
print("=" * 50)
print(f"📈 Total BUY signals: {total_buy_signals}")
print(f"📉 Total SELL signals: {total_sell_signals}")
print(f"🎯 Total actionable signals: {total_actionable_signals}")
print(f"📊 Overall actionable rate: {total_actionable_signals/len(data)*100:.2f}%")

print(f"\n🤖 MODEL PERFORMANCE:")
print("=" * 50)
for name, res in results.items():
    print(f"{name}: {res['accuracy']:.4f} accuracy, {res['cv_mean']:.4f} CV score")

print(f"\n🏆 Best performing model: {best_name}")
print(f"📊 Best accuracy: {best_acc:.4f}")
print(f"🎯 Best CV score: {best_cv:.4f}")

print(f"\n🎉 TRAINING COMPLETED SUCCESSFULLY!")
print("=" * 70)
print(f"✅ All four strategies (S1, S2, S3, S4) trained successfully")
print(f"✅ Best model: {best_name}")
print(f"✅ Training accuracy: {best_acc:.4f}")
print(f"✅ Cross-validation score: {best_cv:.4f}")
print(f"✅ Total actionable signals: {total_actionable_signals}")
print(f"✅ Models saved in: trained_models/")
print(f"\n🚀 Your enhanced trading bot is ready!")
print(f"💡 Use the {best_name} model for best performance.")
print(f"📈 Trained on {len(X_actionable)} actionable signals from all 4 strategies")
print("=" * 70)
