#!/usr/bin/env python3
"""
ML-Integrated Backtesting System for Trading Strategies
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
from ml_strategy_engine import MLStrategyEngine
from utils import (
    fetch_live_candles, print_colored, print_header, print_table_row,
    validate_pair, format_price, format_percentage
)
from config import CURRENCY_PAIRS, STRATEGY_CONFIG, BACKTEST_CONFIG, DISPLAY_CONFIG

class MLBacktestSystem:
    def __init__(self):
        """Initialize the ML backtesting system"""
        self.strategy_engine = MLStrategyEngine()
        self.results = {}
        
    def run_ml_backtest(self, pairs, strategies, num_candles):
        """Run ML-enhanced backtest for specified pairs and strategies"""
        print_header(f"🧠 ML-INTEGRATED BACKTESTING SYSTEM")
        print_colored(f"🤖 Mode: Machine Learning + Rule-based strategies", "SUCCESS")
        print_colored(f"📊 Pairs: {', '.join(pairs)}", "INFO")
        print_colored(f"🔧 Strategies: {', '.join(strategies)}", "INFO") 
        print_colored(f"📈 Candles: {num_candles}", "INFO")
        print_colored(f"🧠 ML Model: {self.strategy_engine.get_ml_model_info()}", "SUCCESS")
        print()
        
        all_results = {}
        
        for pair in pairs:
            print_colored(f"🔄 ML Backtesting {pair}...", "SUCCESS", bold=True)
            
            # Fetch historical data
            df = fetch_live_candles(pair, num_candles)
            
            if df is None or len(df) < 100:
                print_colored(f"❌ Insufficient data for {pair}", "ERROR")
                continue
            
            pair_results = {}
            
            for strategy in strategies:
                print_colored(f"   Testing ML-Enhanced Strategy {strategy}...", "SUCCESS")
                
                # Run ML strategy backtest
                strategy_results = self.backtest_ml_strategy(df, strategy, pair)
                pair_results[strategy] = strategy_results
                
                # Display individual strategy results
                self.display_ml_strategy_results(strategy, strategy_results, pair)
            
            all_results[pair] = pair_results
            print()
        
        # Display overall results
        if len(strategies) > 1:
            self.display_ml_overall_results(all_results, strategies, pairs)
        
        return all_results
    
    def backtest_ml_strategy(self, df, strategy, pair):
        """Backtest a single strategy with ML enhancement"""
        signals = []
        trades = []
        
        # Analyze each candle
        for i in range(100, len(df)):  # Start after enough data for indicators
            current_df = df.iloc[:i+1].copy()
            
            # Get ML-enhanced signal
            ml_result = self.strategy_engine.evaluate_ml_enhanced_strategies(current_df)
            
            # Filter by strategy if specific strategy requested
            if strategy != 'ALL':
                strategy_signals = ml_result['all_signals']
                if strategy in strategy_signals:
                    strategy_signal = strategy_signals[strategy]
                    if strategy_signal['signal'] == 0 or strategy_signal['confidence'] < 0.6:
                        continue  # Skip if this strategy doesn't have a strong signal
            
            # Check if we have a tradeable signal
            if ml_result['signal'] != 'HOLD' and ml_result['confidence'] >= 0.6:
                entry_price = df.iloc[i]['close']
                entry_time = df.iloc[i]['time']
                signal_direction = 1 if ml_result['signal'] == 'BUY' else -1
                
                # Look for exit in next few candles
                exit_found = False
                for j in range(i+1, min(i+6, len(df))):
                    exit_candle = df.iloc[j]
                    
                    # Determine if trade was profitable
                    if signal_direction == 1:  # BUY signal
                        # Check if price went up
                        if exit_candle['high'] > entry_price + BACKTEST_CONFIG['WIN_THRESHOLD']:
                            # Winning trade
                            exit_price = entry_price + BACKTEST_CONFIG['WIN_THRESHOLD']
                            profit = exit_price - entry_price
                            trades.append({
                                'signal': 'BUY',
                                'entry_price': entry_price,
                                'exit_price': exit_price,
                                'profit': profit,
                                'result': 'WIN',
                                'entry_time': entry_time,
                                'exit_time': exit_candle['time'],
                                'confidence': ml_result['confidence'],
                                'ml_confidence': ml_result['ml_confidence'],
                                'rule_confidence': ml_result['rule_confidence'],
                                'decision_method': ml_result['decision_method']
                            })
                            exit_found = True
                            break
                        elif exit_candle['low'] < entry_price - BACKTEST_CONFIG['WIN_THRESHOLD']:
                            # Losing trade
                            exit_price = entry_price - BACKTEST_CONFIG['WIN_THRESHOLD']
                            profit = exit_price - entry_price
                            trades.append({
                                'signal': 'BUY',
                                'entry_price': entry_price,
                                'exit_price': exit_price,
                                'profit': profit,
                                'result': 'LOSS',
                                'entry_time': entry_time,
                                'exit_time': exit_candle['time'],
                                'confidence': ml_result['confidence'],
                                'ml_confidence': ml_result['ml_confidence'],
                                'rule_confidence': ml_result['rule_confidence'],
                                'decision_method': ml_result['decision_method']
                            })
                            exit_found = True
                            break
                    
                    else:  # SELL signal
                        # Check if price went down
                        if exit_candle['low'] < entry_price - BACKTEST_CONFIG['WIN_THRESHOLD']:
                            # Winning trade
                            exit_price = entry_price - BACKTEST_CONFIG['WIN_THRESHOLD']
                            profit = entry_price - exit_price
                            trades.append({
                                'signal': 'SELL',
                                'entry_price': entry_price,
                                'exit_price': exit_price,
                                'profit': profit,
                                'result': 'WIN',
                                'entry_time': entry_time,
                                'exit_time': exit_candle['time'],
                                'confidence': ml_result['confidence'],
                                'ml_confidence': ml_result['ml_confidence'],
                                'rule_confidence': ml_result['rule_confidence'],
                                'decision_method': ml_result['decision_method']
                            })
                            exit_found = True
                            break
                        elif exit_candle['high'] > entry_price + BACKTEST_CONFIG['WIN_THRESHOLD']:
                            # Losing trade
                            exit_price = entry_price + BACKTEST_CONFIG['WIN_THRESHOLD']
                            profit = entry_price - exit_price
                            trades.append({
                                'signal': 'SELL',
                                'entry_price': entry_price,
                                'exit_price': exit_price,
                                'profit': profit,
                                'result': 'LOSS',
                                'entry_time': entry_time,
                                'exit_time': exit_candle['time'],
                                'confidence': ml_result['confidence'],
                                'ml_confidence': ml_result['ml_confidence'],
                                'rule_confidence': ml_result['rule_confidence'],
                                'decision_method': ml_result['decision_method']
                            })
                            exit_found = True
                            break
                
                # If no clear exit found, consider it a neutral trade
                if not exit_found and i+5 < len(df):
                    exit_price = df.iloc[i+5]['close']
                    profit = (exit_price - entry_price) if signal_direction == 1 else (entry_price - exit_price)
                    result = 'WIN' if profit > 0 else 'LOSS'
                    trades.append({
                        'signal': 'BUY' if signal_direction == 1 else 'SELL',
                        'entry_price': entry_price,
                        'exit_price': exit_price,
                        'profit': profit,
                        'result': result,
                        'entry_time': entry_time,
                        'exit_time': df.iloc[i+5]['time'],
                        'confidence': ml_result['confidence'],
                        'ml_confidence': ml_result['ml_confidence'],
                        'rule_confidence': ml_result['rule_confidence'],
                        'decision_method': ml_result['decision_method']
                    })
        
        # Calculate results
        return self.calculate_ml_results(trades)
    
    def calculate_ml_results(self, trades):
        """Calculate ML trading results from trades list"""
        if not trades:
            return {
                'total_signals': 0,
                'buy_signals': 0,
                'sell_signals': 0,
                'buy_wins': 0,
                'buy_losses': 0,
                'sell_wins': 0,
                'sell_losses': 0,
                'buy_accuracy': 0.0,
                'sell_accuracy': 0.0,
                'overall_accuracy': 0.0,
                'total_profit': 0.0,
                'avg_profit_per_trade': 0.0,
                'avg_ml_confidence': 0.0,
                'avg_rule_confidence': 0.0,
                'decision_methods': {}
            }
        
        # Separate buy and sell trades
        buy_trades = [t for t in trades if t['signal'] == 'BUY']
        sell_trades = [t for t in trades if t['signal'] == 'SELL']
        
        # Calculate buy statistics
        buy_wins = len([t for t in buy_trades if t['result'] == 'WIN'])
        buy_losses = len([t for t in buy_trades if t['result'] == 'LOSS'])
        buy_accuracy = (buy_wins / len(buy_trades) * 100) if buy_trades else 0.0
        
        # Calculate sell statistics
        sell_wins = len([t for t in sell_trades if t['result'] == 'WIN'])
        sell_losses = len([t for t in sell_trades if t['result'] == 'LOSS'])
        sell_accuracy = (sell_wins / len(sell_trades) * 100) if sell_trades else 0.0
        
        # Overall statistics
        total_wins = buy_wins + sell_wins
        total_losses = buy_losses + sell_losses
        overall_accuracy = (total_wins / len(trades) * 100) if trades else 0.0
        
        # Profit calculations
        total_profit = sum([t['profit'] for t in trades])
        avg_profit_per_trade = total_profit / len(trades) if trades else 0.0
        
        # ML-specific metrics
        avg_ml_confidence = np.mean([t['ml_confidence'] for t in trades])
        avg_rule_confidence = np.mean([t['rule_confidence'] for t in trades])
        
        # Decision method breakdown
        decision_methods = {}
        for trade in trades:
            method = trade['decision_method']
            if method not in decision_methods:
                decision_methods[method] = 0
            decision_methods[method] += 1
        
        return {
            'total_signals': len(trades),
            'buy_signals': len(buy_trades),
            'sell_signals': len(sell_trades),
            'buy_wins': buy_wins,
            'buy_losses': buy_losses,
            'sell_wins': sell_wins,
            'sell_losses': sell_losses,
            'buy_accuracy': buy_accuracy,
            'sell_accuracy': sell_accuracy,
            'overall_accuracy': overall_accuracy,
            'total_profit': total_profit,
            'avg_profit_per_trade': avg_profit_per_trade,
            'avg_ml_confidence': avg_ml_confidence,
            'avg_rule_confidence': avg_rule_confidence,
            'decision_methods': decision_methods,
            'trades': trades
        }

    def display_ml_strategy_results(self, strategy, results, pair):
        """Display results for a single ML strategy"""
        strategy_name = STRATEGY_CONFIG[strategy]['name'] if strategy in STRATEGY_CONFIG else "ML-Enhanced"

        print_colored(f"   🧠 {strategy} ({strategy_name}) ML Results:", "SUCCESS", bold=True)

        if results['total_signals'] == 0:
            print_colored(f"      ❌ No ML signals generated", "WARNING")
            return

        # Create results table
        headers = ["Metric", "Buy", "Sell", "Overall"]
        widths = [20, 15, 15, 15]
        colors = ["HEADER"] * len(headers)
        print_table_row(headers, widths, colors)
        print_colored("-" * sum(widths), "HEADER")

        # Signals row
        buy_color = "BUY" if results['buy_signals'] > 0 else "INFO"
        sell_color = "SELL" if results['sell_signals'] > 0 else "INFO"
        row_data = ["Total Signals", str(results['buy_signals']), str(results['sell_signals']), str(results['total_signals'])]
        row_colors = ["INFO", buy_color, sell_color, "INFO"]
        print_table_row(row_data, widths, row_colors)

        # Wins row
        row_data = ["Wins", str(results['buy_wins']), str(results['sell_wins']), str(results['buy_wins'] + results['sell_wins'])]
        row_colors = ["INFO", "SUCCESS", "SUCCESS", "SUCCESS"]
        print_table_row(row_data, widths, row_colors)

        # Losses row
        row_data = ["Losses", str(results['buy_losses']), str(results['sell_losses']), str(results['buy_losses'] + results['sell_losses'])]
        row_colors = ["INFO", "ERROR", "ERROR", "ERROR"]
        print_table_row(row_data, widths, row_colors)

        # Accuracy row
        row_data = ["Accuracy", f"{results['buy_accuracy']:.1f}%", f"{results['sell_accuracy']:.1f}%", f"{results['overall_accuracy']:.1f}%"]
        accuracy_color = "SUCCESS" if results['overall_accuracy'] >= 60 else "WARNING" if results['overall_accuracy'] >= 50 else "ERROR"
        row_colors = ["INFO", accuracy_color, accuracy_color, accuracy_color]
        print_table_row(row_data, widths, row_colors)

        # ML-specific metrics
        print_colored("-" * sum(widths), "HEADER")
        row_data = ["ML Confidence", f"{results['avg_ml_confidence']*100:.1f}%", f"{results['avg_ml_confidence']*100:.1f}%", f"{results['avg_ml_confidence']*100:.1f}%"]
        row_colors = ["INFO", "SUCCESS", "SUCCESS", "SUCCESS"]
        print_table_row(row_data, widths, row_colors)

        row_data = ["Rule Confidence", f"{results['avg_rule_confidence']*100:.1f}%", f"{results['avg_rule_confidence']*100:.1f}%", f"{results['avg_rule_confidence']*100:.1f}%"]
        row_colors = ["INFO", "INFO", "INFO", "INFO"]
        print_table_row(row_data, widths, row_colors)

        # Decision methods
        print_colored(f"      🤖 Decision Methods:", "SUCCESS")
        for method, count in results['decision_methods'].items():
            percentage = (count / results['total_signals']) * 100
            print_colored(f"         {method}: {count} ({percentage:.1f}%)", "INFO")

        print()

    def display_ml_overall_results(self, all_results, strategies, pairs):
        """Display combined ML results for multiple strategies"""
        print_header("🧠 OVERALL ML-INTEGRATED BACKTEST RESULTS")

        # Combine all results
        combined_results = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'buy_wins': 0,
            'buy_losses': 0,
            'sell_wins': 0,
            'sell_losses': 0,
            'total_profit': 0.0,
            'ml_confidence_sum': 0.0,
            'rule_confidence_sum': 0.0,
            'decision_methods': {}
        }

        for pair in pairs:
            if pair in all_results:
                for strategy in strategies:
                    if strategy in all_results[pair]:
                        results = all_results[pair][strategy]
                        combined_results['total_signals'] += results['total_signals']
                        combined_results['buy_signals'] += results['buy_signals']
                        combined_results['sell_signals'] += results['sell_signals']
                        combined_results['buy_wins'] += results['buy_wins']
                        combined_results['buy_losses'] += results['buy_losses']
                        combined_results['sell_wins'] += results['sell_wins']
                        combined_results['sell_losses'] += results['sell_losses']
                        combined_results['total_profit'] += results['total_profit']
                        combined_results['ml_confidence_sum'] += results['avg_ml_confidence'] * results['total_signals']
                        combined_results['rule_confidence_sum'] += results['avg_rule_confidence'] * results['total_signals']

                        # Combine decision methods
                        for method, count in results['decision_methods'].items():
                            if method not in combined_results['decision_methods']:
                                combined_results['decision_methods'][method] = 0
                            combined_results['decision_methods'][method] += count

        # Calculate combined accuracies
        buy_accuracy = (combined_results['buy_wins'] / combined_results['buy_signals'] * 100) if combined_results['buy_signals'] > 0 else 0.0
        sell_accuracy = (combined_results['sell_wins'] / combined_results['sell_signals'] * 100) if combined_results['sell_signals'] > 0 else 0.0
        overall_accuracy = ((combined_results['buy_wins'] + combined_results['sell_wins']) / combined_results['total_signals'] * 100) if combined_results['total_signals'] > 0 else 0.0

        avg_ml_confidence = (combined_results['ml_confidence_sum'] / combined_results['total_signals']) if combined_results['total_signals'] > 0 else 0.0
        avg_rule_confidence = (combined_results['rule_confidence_sum'] / combined_results['total_signals']) if combined_results['total_signals'] > 0 else 0.0

        # Display combined results table
        headers = ["Metric", "Buy", "Sell", "Overall"]
        widths = [20, 15, 15, 15]
        colors = ["HEADER"] * len(headers)
        print_table_row(headers, widths, colors)
        print_colored("=" * sum(widths), "HEADER")

        # Total signals
        row_data = ["Total Signals", str(combined_results['buy_signals']), str(combined_results['sell_signals']), str(combined_results['total_signals'])]
        row_colors = ["INFO", "BUY", "SELL", "INFO"]
        print_table_row(row_data, widths, row_colors)

        # Wins
        row_data = ["Total Wins", str(combined_results['buy_wins']), str(combined_results['sell_wins']), str(combined_results['buy_wins'] + combined_results['sell_wins'])]
        row_colors = ["INFO", "SUCCESS", "SUCCESS", "SUCCESS"]
        print_table_row(row_data, widths, row_colors)

        # Losses
        row_data = ["Total Losses", str(combined_results['buy_losses']), str(combined_results['sell_losses']), str(combined_results['buy_losses'] + combined_results['sell_losses'])]
        row_colors = ["INFO", "ERROR", "ERROR", "ERROR"]
        print_table_row(row_data, widths, row_colors)

        # Accuracy
        accuracy_color = "SUCCESS" if overall_accuracy >= 60 else "WARNING" if overall_accuracy >= 50 else "ERROR"
        row_data = ["Accuracy", f"{buy_accuracy:.1f}%", f"{sell_accuracy:.1f}%", f"{overall_accuracy:.1f}%"]
        row_colors = ["INFO", accuracy_color, accuracy_color, accuracy_color]
        print_table_row(row_data, widths, row_colors)

        # ML metrics
        print_colored("=" * sum(widths), "HEADER")
        row_data = ["ML Confidence", f"{avg_ml_confidence*100:.1f}%", f"{avg_ml_confidence*100:.1f}%", f"{avg_ml_confidence*100:.1f}%"]
        row_colors = ["INFO", "SUCCESS", "SUCCESS", "SUCCESS"]
        print_table_row(row_data, widths, row_colors)

        row_data = ["Rule Confidence", f"{avg_rule_confidence*100:.1f}%", f"{avg_rule_confidence*100:.1f}%", f"{avg_rule_confidence*100:.1f}%"]
        row_colors = ["INFO", "INFO", "INFO", "INFO"]
        print_table_row(row_data, widths, row_colors)

        print_colored("=" * sum(widths), "HEADER")

        # Decision methods breakdown
        print_colored(f"🤖 ML Decision Methods:", "SUCCESS", bold=True)
        for method, count in combined_results['decision_methods'].items():
            percentage = (count / combined_results['total_signals']) * 100
            print_colored(f"   {method}: {count} signals ({percentage:.1f}%)", "SUCCESS")

        # Summary
        if overall_accuracy >= 60:
            print_colored(f"\n🎉 Excellent ML performance! Overall accuracy: {overall_accuracy:.1f}%", "SUCCESS", bold=True)
        elif overall_accuracy >= 50:
            print_colored(f"\n👍 Good ML performance! Overall accuracy: {overall_accuracy:.1f}%", "WARNING", bold=True)
        else:
            print_colored(f"\n⚠️  ML needs improvement. Overall accuracy: {overall_accuracy:.1f}%", "ERROR", bold=True)

        print()

# User input and main function (similar to backtest_system.py but for ML)
def get_user_input():
    """Get user input for ML backtesting parameters"""
    print_header("🧠 ML BACKTEST CONFIGURATION")

    # Get number of candles
    while True:
        try:
            print_colored("📈 Enter number of candles to backtest:", "SUCCESS")
            print_colored(f"   (Default: {BACKTEST_CONFIG['DEFAULT_CANDLES']}, Max: {BACKTEST_CONFIG['MAX_CANDLES']})", "INFO")
            candles_input = input("   Candles: ").strip()

            if not candles_input:
                num_candles = BACKTEST_CONFIG['DEFAULT_CANDLES']
            else:
                num_candles = int(candles_input)

            if num_candles < 100:
                print_colored("❌ Minimum 100 candles required", "ERROR")
                continue
            elif num_candles > BACKTEST_CONFIG['MAX_CANDLES']:
                print_colored(f"❌ Maximum {BACKTEST_CONFIG['MAX_CANDLES']} candles allowed", "ERROR")
                continue

            break
        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")

    # Get currency pairs
    print_colored("\n💱 Available currency pairs:", "SUCCESS")
    for i, pair in enumerate(CURRENCY_PAIRS, 1):
        print_colored(f"   {i}. {pair}", "INFO")

    while True:
        print_colored("\nEnter pair numbers (comma-separated) or 'all' for all pairs:", "SUCCESS")
        pairs_input = input("   Pairs: ").strip().lower()

        if pairs_input == 'all':
            selected_pairs = CURRENCY_PAIRS.copy()
            break
        else:
            try:
                pair_indices = [int(x.strip()) for x in pairs_input.split(',')]
                selected_pairs = []

                for idx in pair_indices:
                    if 1 <= idx <= len(CURRENCY_PAIRS):
                        selected_pairs.append(CURRENCY_PAIRS[idx-1])
                    else:
                        print_colored(f"❌ Invalid pair number: {idx}", "ERROR")
                        raise ValueError

                if selected_pairs:
                    break
                else:
                    print_colored("❌ No valid pairs selected", "ERROR")

            except ValueError:
                print_colored("❌ Please enter valid pair numbers", "ERROR")

    # Get strategies
    print_colored("\n🧠 Available ML-enhanced strategies:", "SUCCESS")
    available_strategies = ['S1', 'S2', 'S3', 'S4']
    for i, strategy in enumerate(available_strategies, 1):
        strategy_name = STRATEGY_CONFIG[strategy]['name']
        print_colored(f"   {i}. {strategy} - {strategy_name} (ML-Enhanced)", "SUCCESS")

    while True:
        print_colored("\nEnter strategy numbers (comma-separated) or 'all' for all strategies:", "SUCCESS")
        strategies_input = input("   Strategies: ").strip().lower()

        if strategies_input == 'all':
            selected_strategies = available_strategies.copy()
            break
        else:
            try:
                strategy_indices = [int(x.strip()) for x in strategies_input.split(',')]
                selected_strategies = []

                for idx in strategy_indices:
                    if 1 <= idx <= len(available_strategies):
                        selected_strategies.append(available_strategies[idx-1])
                    else:
                        print_colored(f"❌ Invalid strategy number: {idx}", "ERROR")
                        raise ValueError

                if selected_strategies:
                    break
                else:
                    print_colored("❌ No valid strategies selected", "ERROR")

            except ValueError:
                print_colored("❌ Please enter valid strategy numbers", "ERROR")

    return num_candles, selected_pairs, selected_strategies

def main():
    """Main function for ML backtesting"""
    try:
        print_colored("🧠 ML-Integrated Trading Strategy Backtesting System", "SUCCESS", bold=True)
        print_colored("=" * 60, "HEADER")
        print()

        # Get user input
        num_candles, pairs, strategies = get_user_input()

        # Confirm settings
        print_header("📋 ML BACKTEST SETTINGS CONFIRMATION")
        print_colored(f"📈 Candles: {num_candles}", "SUCCESS")
        print_colored(f"💱 Pairs: {', '.join(pairs)}", "SUCCESS")
        print_colored(f"🧠 ML Strategies: {', '.join(strategies)}", "SUCCESS")
        print()

        confirm = input("Proceed with ML backtest? (y/n): ").strip().lower()
        if confirm != 'y':
            print_colored("❌ ML backtest cancelled", "WARNING")
            return

        # Run ML backtest
        ml_backtest_system = MLBacktestSystem()
        results = ml_backtest_system.run_ml_backtest(pairs, strategies, num_candles)

        print_header("🎉 ML BACKTEST COMPLETED")
        print_colored("ML-enhanced results have been displayed above.", "SUCCESS")

    except KeyboardInterrupt:
        print_colored("\n⚠️  ML backtest interrupted by user", "WARNING")
    except Exception as e:
        print_colored(f"❌ Error during ML backtest: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
