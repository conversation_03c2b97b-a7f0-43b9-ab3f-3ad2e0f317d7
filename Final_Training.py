#!/usr/bin/env python3
"""
Final Trading Bot Training System for Strategies 1, 3, and 4
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, accuracy_score, confusion_matrix
import joblib
import os
from datetime import datetime

print("🚀 Final Trading Bot Training System")
print("Training on Strategies 1, 3, and 4")
print("=" * 60)

# Load and process each strategy
strategies = {
    'S1': 'output_signals_S1.csv',
    'S3': 'output_signals_S3.csv', 
    'S4': 'output_signals_S4.csv'
}

strategy_names = {
    'S1': 'Breakout with Volume',
    'S3': 'Support/Resistance Rejection',
    'S4': 'Trendline Break with Rejection'
}

print("\n📊 Loading Strategy Data...")
all_data = []

for strategy, filename in strategies.items():
    print(f"Loading {filename}...")
    df = pd.read_csv(filename)
    
    # Add strategy identifier
    df['strategy_source'] = strategy
    
    # Fill NaN values in strategy signal column
    signal_col = f'strategy{strategy[1:]}_signal'
    df[signal_col] = df[signal_col].fillna(0)
    
    # Count signals
    signal_counts = df[signal_col].value_counts()
    buy_signals = signal_counts.get(1, 0)
    sell_signals = signal_counts.get(-1, 0)
    hold_signals = signal_counts.get(0, 0)
    
    print(f"✅ {strategy} ({strategy_names[strategy]}): {len(df)} rows")
    print(f"   📈 BUY signals: {buy_signals}")
    print(f"   📉 SELL signals: {sell_signals}")
    print(f"   ⏸️ HOLD signals: {hold_signals}")
    
    all_data.append(df)

# Combine all data
print(f"\n🔄 Combining all strategy data...")
data = pd.concat(all_data, ignore_index=True)
print(f"✅ Combined dataset: {len(data)} rows")

# Create labels based on strategy signals
print("\n🏷️ Creating trading labels...")
def create_label(row):
    strategy = row['strategy_source']
    signal_col = f'strategy{strategy[1:]}_signal'
    signal = row[signal_col]
    
    if signal == 1:
        return 'BUY'
    elif signal == -1:
        return 'SELL'
    else:
        return 'HOLD'

data['trading_signal'] = data.apply(create_label, axis=1)
label_dist = data['trading_signal'].value_counts()
print(f"✅ Label distribution:")
for label, count in label_dist.items():
    print(f"   {label}: {count} ({count/len(data)*100:.1f}%)")

# Filter for actionable signals only (BUY/SELL)
actionable = data[data['trading_signal'].isin(['BUY', 'SELL'])].copy()
print(f"\n✅ Actionable signals: {len(actionable)} rows")

if len(actionable) < 100:
    print("❌ Not enough actionable signals for training!")
    exit(1)

# Feature engineering
print("\n⚙️ Engineering features...")
def engineer_features(df):
    df = df.copy()
    
    # Basic candle features
    df['body_size'] = abs(df['close'] - df['open'])
    df['upper_wick'] = df['high'] - df[['open', 'close']].max(axis=1)
    df['lower_wick'] = df[['open', 'close']].min(axis=1) - df['low']
    df['total_range'] = df['high'] - df['low']
    
    # Ratios and percentages
    df['body_to_range'] = df['body_size'] / (df['total_range'] + 1e-8)
    df['upper_wick_pct'] = df['upper_wick'] / (df['body_size'] + 1e-8)
    df['lower_wick_pct'] = df['lower_wick'] / (df['body_size'] + 1e-8)
    
    # Volume features
    df['volume_price_trend'] = df['volume'] * (df['close'] - df['open'])
    df['volume_normalized'] = df['volume'] / (df['volume_sma'] + 1e-8)
    
    # Price momentum
    df['price_change_pct'] = df['price_change'] / (df['open'] + 1e-8)
    df['momentum_3'] = df['close'].pct_change(3).fillna(0)
    df['momentum_5'] = df['close'].pct_change(5).fillna(0)
    
    # Volatility
    df['volatility_5'] = df['close'].rolling(5).std().fillna(0)
    df['volatility_10'] = df['close'].rolling(10).std().fillna(0)
    
    # Candle patterns
    df['is_bullish'] = (df['close'] > df['open']).astype(int)
    df['is_bearish'] = (df['close'] < df['open']).astype(int)
    df['is_doji'] = (abs(df['close'] - df['open']) < 0.0001).astype(int)
    
    # Technical indicator features
    df['rsi_oversold'] = (df['rsi'] < 30).astype(int)
    df['rsi_overbought'] = (df['rsi'] > 70).astype(int)
    df['bb_squeeze'] = (df['bb_width'] < df['bb_width'].rolling(20).mean()).astype(int)
    
    return df

actionable = engineer_features(actionable)
print("✅ Features engineered")

# Clean data
actionable = actionable.replace([np.inf, -np.inf], np.nan)
actionable = actionable.fillna(actionable.select_dtypes(include=[np.number]).mean())
print("✅ Data cleaned")

# Prepare features
exclude_cols = [
    'time', 'direction', 'trading_signal', 'strategy_source',
    'strategy1_signal', 'strategy3_signal', 'strategy4_signal'
]

feature_cols = [col for col in actionable.columns 
                if col not in exclude_cols and actionable[col].dtype in ['int64', 'float64']]

print(f"✅ Selected {len(feature_cols)} features for training")

# Prepare data
X = actionable[feature_cols].values
y = actionable['trading_signal'].values

# Encode labels
le = LabelEncoder()
y_encoded = le.fit_transform(y)
print(f"✅ Labels encoded: {dict(zip(le.classes_, range(len(le.classes_))))}")

# Scale features
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)
print("✅ Features scaled")

# Train/test split
X_train, X_test, y_train, y_test = train_test_split(
    X_scaled, y_encoded, test_size=0.2, random_state=42, stratify=y_encoded
)

print(f"\n📊 Dataset split:")
print(f"   Training: {len(X_train)} samples")
print(f"   Testing: {len(X_test)} samples")

# Train models
print(f"\n🤖 Training models...")

models = {
    'RandomForest': RandomForestClassifier(
        n_estimators=200, max_depth=15, min_samples_split=5,
        random_state=42, n_jobs=-1
    ),
    'GradientBoosting': GradientBoostingClassifier(
        n_estimators=150, learning_rate=0.1, max_depth=8,
        random_state=42
    ),
    'LogisticRegression': LogisticRegression(
        random_state=42, max_iter=1000, C=1.0
    )
}

trained_models = {}
results = {}

for name, model in models.items():
    print(f"\n🔄 Training {name}...")
    
    # Train
    model.fit(X_train, y_train)
    
    # Evaluate
    y_pred = model.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)
    
    # Cross-validation
    cv_scores = cross_val_score(model, X_train, y_train, cv=5, scoring='accuracy')
    
    print(f"   ✅ Accuracy: {accuracy:.4f}")
    print(f"   ✅ CV Score: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
    
    trained_models[name] = model
    results[name] = {
        'accuracy': accuracy,
        'cv_mean': cv_scores.mean(),
        'cv_std': cv_scores.std(),
        'predictions': y_pred
    }

# Create ensemble
print(f"\n🎭 Creating ensemble model...")
ensemble = VotingClassifier(
    estimators=[(name, model) for name, model in trained_models.items()],
    voting='soft'
)
ensemble.fit(X_train, y_train)

ensemble_pred = ensemble.predict(X_test)
ensemble_acc = accuracy_score(y_test, ensemble_pred)
ensemble_cv = cross_val_score(ensemble, X_train, y_train, cv=5, scoring='accuracy')

print(f"✅ Ensemble accuracy: {ensemble_acc:.4f}")
print(f"✅ Ensemble CV: {ensemble_cv.mean():.4f} ± {ensemble_cv.std():.4f}")

trained_models['Ensemble'] = ensemble
results['Ensemble'] = {
    'accuracy': ensemble_acc,
    'cv_mean': ensemble_cv.mean(),
    'cv_std': ensemble_cv.std(),
    'predictions': ensemble_pred
}

# Find best model
best_model_name = max(results.keys(), key=lambda x: results[x]['accuracy'])
best_accuracy = results[best_model_name]['accuracy']

print(f"\n🏆 Best performing model: {best_model_name}")
print(f"🎯 Best accuracy: {best_accuracy:.4f}")

# Detailed evaluation
print(f"\n📋 Detailed Classification Report ({best_model_name}):")
print("-" * 60)
best_pred = results[best_model_name]['predictions']
print(classification_report(y_test, best_pred, target_names=le.classes_))

print(f"\n🔍 Confusion Matrix ({best_model_name}):")
cm = confusion_matrix(y_test, best_pred)
print(cm)

# Strategy-specific performance
print(f"\n📈 Strategy-specific performance:")
print("-" * 40)

# Create a simple mapping approach
# Since we used train_test_split, we need to track which samples belong to which strategy
actionable_reset = actionable.reset_index(drop=True)
X_with_strategy = actionable_reset[feature_cols + ['strategy_source']]
y_with_strategy = actionable_reset['trading_signal']

# Re-split with the same random state to get consistent splits
X_train_full, X_test_full, y_train_full, y_test_full = train_test_split(
    X_with_strategy, y_with_strategy, test_size=0.2, random_state=42,
    stratify=le.transform(y_with_strategy)
)

# Now we can analyze by strategy
for strategy in strategies.keys():
    strategy_test_mask = X_test_full['strategy_source'] == strategy
    strategy_count = strategy_test_mask.sum()

    if strategy_count > 0:
        # Get predictions for this strategy
        strategy_positions = np.where(strategy_test_mask)[0]
        strategy_y_test = y_test[strategy_positions]
        strategy_y_pred = best_pred[strategy_positions]
        strategy_acc = accuracy_score(strategy_y_test, strategy_y_pred)

        print(f"✅ {strategy} ({strategy_names[strategy]}): {strategy_acc:.4f} "
              f"({strategy_count} samples)")
    else:
        print(f"⚠️ {strategy} ({strategy_names[strategy]}): No test samples")

# Save models and components
print(f"\n💾 Saving trained models...")
os.makedirs('trained_models', exist_ok=True)

# Save all models
for name, model in trained_models.items():
    filename = f'trained_models/{name.lower()}_model.pkl'
    joblib.dump(model, filename)
    print(f"✅ Saved {name} → {filename}")

# Save preprocessing components
joblib.dump(scaler, 'trained_models/feature_scaler.pkl')
joblib.dump(le, 'trained_models/label_encoder.pkl')
joblib.dump(feature_cols, 'trained_models/feature_columns.pkl')

print(f"✅ Saved preprocessing components")

# Save training summary
summary = {
    'model_results': results,
    'best_model': best_model_name,
    'best_accuracy': best_accuracy,
    'strategies_used': list(strategies.keys()),
    'feature_count': len(feature_cols),
    'training_samples': len(X_train),
    'test_samples': len(X_test),
    'actionable_signals': len(actionable),
    'total_data_points': len(data),
    'timestamp': datetime.now().isoformat()
}

joblib.dump(summary, 'trained_models/training_summary.pkl')
print(f"✅ Saved training summary")

# Final summary
print(f"\n🎉 TRAINING COMPLETED SUCCESSFULLY!")
print("=" * 60)
print(f"🏆 Best Model: {best_model_name}")
print(f"📊 Best Accuracy: {best_accuracy:.4f}")
print(f"🎯 Features Used: {len(feature_cols)}")
print(f"📈 Training Samples: {len(X_train)}")
print(f"🧪 Test Samples: {len(X_test)}")
print(f"🔧 Strategies: {', '.join(strategies.keys())}")
print(f"📁 Models saved in: trained_models/")
print(f"\n✨ Your trading bot is now trained and ready to use!")
print("🚀 Use the ensemble model for best performance.")
print("=" * 60)
