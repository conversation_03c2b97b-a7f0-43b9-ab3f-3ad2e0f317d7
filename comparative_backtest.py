#!/usr/bin/env python3
"""
Comparative Backtesting System
Compares Rule-based vs ML-integrated trading strategies side-by-side
"""

import pandas as pd
import numpy as np
from datetime import datetime
from backtest_system import BacktestSystem
from ml_backtest_system import MLBacktestSystem
from utils import (
    print_colored, print_header, print_table_row,
    format_percentage
)
from config import CURRENCY_PAIRS, STRATEGY_CONFIG, BACKTEST_CONFIG

class ComparativeBacktest:
    def __init__(self):
        """Initialize the comparative backtesting system"""
        self.rule_based_system = BacktestSystem()
        self.ml_system = MLBacktestSystem()
        
    def run_comparative_backtest(self, pairs, strategies, num_candles):
        """Run comparative backtest between rule-based and ML systems"""
        print_header(f"🔄 COMPARATIVE BACKTESTING SYSTEM")
        print_colored(f"📊 Comparing: Rule-based vs ML-integrated strategies", "HEADER")
        print_colored(f"💱 Pairs: {', '.join(pairs)}", "INFO")
        print_colored(f"🔧 Strategies: {', '.join(strategies)}", "INFO") 
        print_colored(f"📈 Candles: {num_candles}", "INFO")
        print()
        
        # Run both backtests
        print_colored("🔄 Running Rule-based Backtest...", "INFO", bold=True)
        rule_results = self.rule_based_system.run_backtest(pairs, strategies, num_candles)
        
        print_colored("\n🔄 Running ML-integrated Backtest...", "SUCCESS", bold=True)
        ml_results = self.ml_system.run_ml_backtest(pairs, strategies, num_candles)
        
        # Compare results
        print_header("🔄 COMPARATIVE ANALYSIS")
        self.display_comparative_results(rule_results, ml_results, pairs, strategies)
        
        return rule_results, ml_results
    
    def display_comparative_results(self, rule_results, ml_results, pairs, strategies):
        """Display side-by-side comparison of results"""
        
        # Calculate combined statistics for both systems
        rule_combined = self.calculate_combined_stats(rule_results, pairs, strategies)
        ml_combined = self.calculate_combined_stats(ml_results, pairs, strategies, is_ml=True)
        
        # Display comparison table
        print_colored("📊 RULE-BASED vs ML-INTEGRATED COMPARISON", "HEADER", bold=True)
        print_colored("=" * 80, "HEADER")
        
        headers = ["Metric", "Rule-Based", "ML-Integrated", "Difference", "Winner"]
        widths = [20, 15, 15, 15, 15]
        colors = ["HEADER"] * len(headers)
        print_table_row(headers, widths, colors)
        print_colored("-" * sum(widths), "HEADER")
        
        # Total signals comparison
        rule_signals = rule_combined['total_signals']
        ml_signals = ml_combined['total_signals']
        signal_diff = ml_signals - rule_signals
        signal_winner = "ML" if ml_signals > rule_signals else "Rule" if rule_signals > ml_signals else "Tie"
        
        row_data = [
            "Total Signals",
            str(rule_signals),
            str(ml_signals),
            f"+{signal_diff}" if signal_diff > 0 else str(signal_diff),
            signal_winner
        ]
        row_colors = ["INFO", "INFO", "SUCCESS", "SUCCESS" if signal_diff > 0 else "ERROR" if signal_diff < 0 else "INFO", "SUCCESS" if signal_winner == "ML" else "BUY" if signal_winner == "Rule" else "INFO"]
        print_table_row(row_data, widths, row_colors)
        
        # Accuracy comparison
        rule_accuracy = rule_combined['overall_accuracy']
        ml_accuracy = ml_combined['overall_accuracy']
        accuracy_diff = ml_accuracy - rule_accuracy
        accuracy_winner = "ML" if ml_accuracy > rule_accuracy else "Rule" if rule_accuracy > ml_accuracy else "Tie"
        
        row_data = [
            "Overall Accuracy",
            f"{rule_accuracy:.1f}%",
            f"{ml_accuracy:.1f}%",
            f"+{accuracy_diff:.1f}%" if accuracy_diff > 0 else f"{accuracy_diff:.1f}%",
            accuracy_winner
        ]
        row_colors = ["INFO", "BUY", "SUCCESS", "SUCCESS" if accuracy_diff > 0 else "ERROR" if accuracy_diff < 0 else "INFO", "SUCCESS" if accuracy_winner == "ML" else "BUY" if accuracy_winner == "Rule" else "INFO"]
        print_table_row(row_data, widths, row_colors)
        
        # Buy accuracy comparison
        rule_buy_acc = rule_combined['buy_accuracy']
        ml_buy_acc = ml_combined['buy_accuracy']
        buy_acc_diff = ml_buy_acc - rule_buy_acc
        buy_acc_winner = "ML" if ml_buy_acc > rule_buy_acc else "Rule" if rule_buy_acc > ml_buy_acc else "Tie"
        
        row_data = [
            "Buy Accuracy",
            f"{rule_buy_acc:.1f}%",
            f"{ml_buy_acc:.1f}%",
            f"+{buy_acc_diff:.1f}%" if buy_acc_diff > 0 else f"{buy_acc_diff:.1f}%",
            buy_acc_winner
        ]
        row_colors = ["INFO", "BUY", "SUCCESS", "SUCCESS" if buy_acc_diff > 0 else "ERROR" if buy_acc_diff < 0 else "INFO", "SUCCESS" if buy_acc_winner == "ML" else "BUY" if buy_acc_winner == "Rule" else "INFO"]
        print_table_row(row_data, widths, row_colors)
        
        # Sell accuracy comparison
        rule_sell_acc = rule_combined['sell_accuracy']
        ml_sell_acc = ml_combined['sell_accuracy']
        sell_acc_diff = ml_sell_acc - rule_sell_acc
        sell_acc_winner = "ML" if ml_sell_acc > rule_sell_acc else "Rule" if rule_sell_acc > ml_sell_acc else "Tie"
        
        row_data = [
            "Sell Accuracy",
            f"{rule_sell_acc:.1f}%",
            f"{ml_sell_acc:.1f}%",
            f"+{sell_acc_diff:.1f}%" if sell_acc_diff > 0 else f"{sell_acc_diff:.1f}%",
            sell_acc_winner
        ]
        row_colors = ["INFO", "SELL", "SUCCESS", "SUCCESS" if sell_acc_diff > 0 else "ERROR" if sell_acc_diff < 0 else "INFO", "SUCCESS" if sell_acc_winner == "ML" else "SELL" if sell_acc_winner == "Rule" else "INFO"]
        print_table_row(row_data, widths, row_colors)
        
        # Win rate comparison
        rule_win_rate = (rule_combined['total_wins'] / rule_combined['total_signals'] * 100) if rule_combined['total_signals'] > 0 else 0
        ml_win_rate = (ml_combined['total_wins'] / ml_combined['total_signals'] * 100) if ml_combined['total_signals'] > 0 else 0
        win_rate_diff = ml_win_rate - rule_win_rate
        win_rate_winner = "ML" if ml_win_rate > rule_win_rate else "Rule" if rule_win_rate > ml_win_rate else "Tie"
        
        row_data = [
            "Win Rate",
            f"{rule_win_rate:.1f}%",
            f"{ml_win_rate:.1f}%",
            f"+{win_rate_diff:.1f}%" if win_rate_diff > 0 else f"{win_rate_diff:.1f}%",
            win_rate_winner
        ]
        row_colors = ["INFO", "INFO", "SUCCESS", "SUCCESS" if win_rate_diff > 0 else "ERROR" if win_rate_diff < 0 else "INFO", "SUCCESS" if win_rate_winner == "ML" else "BUY" if win_rate_winner == "Rule" else "INFO"]
        print_table_row(row_data, widths, row_colors)
        
        print_colored("=" * sum(widths), "HEADER")
        
        # ML-specific metrics (if available)
        if 'avg_ml_confidence' in ml_combined:
            print_colored(f"🤖 ML-Specific Metrics:", "SUCCESS", bold=True)
            print_colored(f"   Average ML Confidence: {ml_combined['avg_ml_confidence']*100:.1f}%", "SUCCESS")
            print_colored(f"   Average Rule Confidence: {ml_combined['avg_rule_confidence']*100:.1f}%", "INFO")
            
            if 'decision_methods' in ml_combined:
                print_colored(f"   ML Decision Methods:", "SUCCESS")
                for method, count in ml_combined['decision_methods'].items():
                    percentage = (count / ml_combined['total_signals']) * 100 if ml_combined['total_signals'] > 0 else 0
                    print_colored(f"      {method}: {count} ({percentage:.1f}%)", "INFO")
        
        # Overall winner determination
        print_colored(f"\n🏆 OVERALL PERFORMANCE COMPARISON:", "HEADER", bold=True)
        
        # Score calculation (accuracy weighted more heavily)
        rule_score = rule_accuracy * 0.6 + rule_win_rate * 0.4
        ml_score = ml_accuracy * 0.6 + ml_win_rate * 0.4
        
        if ml_score > rule_score + 2:  # ML significantly better
            print_colored(f"🥇 Winner: ML-Integrated System", "SUCCESS", bold=True)
            print_colored(f"   ML Score: {ml_score:.1f} vs Rule Score: {rule_score:.1f}", "SUCCESS")
            print_colored(f"   🧠 Machine Learning provides superior performance!", "SUCCESS")
        elif rule_score > ml_score + 2:  # Rule-based significantly better
            print_colored(f"🥇 Winner: Rule-Based System", "BUY", bold=True)
            print_colored(f"   Rule Score: {rule_score:.1f} vs ML Score: {ml_score:.1f}", "BUY")
            print_colored(f"   📊 Traditional rule-based strategies are more effective!", "BUY")
        else:  # Close performance
            print_colored(f"🤝 Result: Close Performance", "WARNING", bold=True)
            print_colored(f"   Rule Score: {rule_score:.1f} vs ML Score: {ml_score:.1f}", "WARNING")
            print_colored(f"   ⚖️  Both approaches show similar effectiveness!", "WARNING")
        
        # Recommendations
        print_colored(f"\n💡 RECOMMENDATIONS:", "INFO", bold=True)
        
        if ml_score > rule_score:
            print_colored(f"   ✅ Consider using ML-integrated trading for better accuracy", "SUCCESS")
            print_colored(f"   ✅ ML models show {accuracy_diff:.1f}% accuracy improvement", "SUCCESS")
        else:
            print_colored(f"   ✅ Rule-based system provides reliable performance", "BUY")
            print_colored(f"   ✅ Simpler implementation with {rule_accuracy:.1f}% accuracy", "BUY")
        
        if ml_signals > rule_signals:
            print_colored(f"   📊 ML system generates {signal_diff} more trading opportunities", "INFO")
        elif rule_signals > ml_signals:
            print_colored(f"   📊 Rule-based system is more selective with {abs(signal_diff)} fewer signals", "INFO")
        
        print()
    
    def calculate_combined_stats(self, results, pairs, strategies, is_ml=False):
        """Calculate combined statistics from backtest results"""
        combined = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'buy_wins': 0,
            'buy_losses': 0,
            'sell_wins': 0,
            'sell_losses': 0,
            'total_wins': 0,
            'total_losses': 0
        }
        
        if is_ml:
            combined.update({
                'ml_confidence_sum': 0.0,
                'rule_confidence_sum': 0.0,
                'decision_methods': {}
            })
        
        for pair in pairs:
            if pair in results:
                for strategy in strategies:
                    if strategy in results[pair]:
                        result = results[pair][strategy]
                        combined['total_signals'] += result['total_signals']
                        combined['buy_signals'] += result['buy_signals']
                        combined['sell_signals'] += result['sell_signals']
                        combined['buy_wins'] += result['buy_wins']
                        combined['buy_losses'] += result['buy_losses']
                        combined['sell_wins'] += result['sell_wins']
                        combined['sell_losses'] += result['sell_losses']
                        
                        if is_ml and 'avg_ml_confidence' in result:
                            combined['ml_confidence_sum'] += result['avg_ml_confidence'] * result['total_signals']
                            combined['rule_confidence_sum'] += result['avg_rule_confidence'] * result['total_signals']
                            
                            if 'decision_methods' in result:
                                for method, count in result['decision_methods'].items():
                                    if method not in combined['decision_methods']:
                                        combined['decision_methods'][method] = 0
                                    combined['decision_methods'][method] += count
        
        # Calculate derived metrics
        combined['total_wins'] = combined['buy_wins'] + combined['sell_wins']
        combined['total_losses'] = combined['buy_losses'] + combined['sell_losses']
        
        combined['buy_accuracy'] = (combined['buy_wins'] / combined['buy_signals'] * 100) if combined['buy_signals'] > 0 else 0.0
        combined['sell_accuracy'] = (combined['sell_wins'] / combined['sell_signals'] * 100) if combined['sell_signals'] > 0 else 0.0
        combined['overall_accuracy'] = (combined['total_wins'] / combined['total_signals'] * 100) if combined['total_signals'] > 0 else 0.0
        
        if is_ml and combined['total_signals'] > 0:
            combined['avg_ml_confidence'] = combined['ml_confidence_sum'] / combined['total_signals']
            combined['avg_rule_confidence'] = combined['rule_confidence_sum'] / combined['total_signals']
        
        return combined

def get_user_input():
    """Get user input for comparative backtesting"""
    print_header("🔄 COMPARATIVE BACKTEST CONFIGURATION")
    
    # Get number of candles
    while True:
        try:
            print_colored("📈 Enter number of candles to backtest:", "HEADER")
            print_colored(f"   (Default: {BACKTEST_CONFIG['DEFAULT_CANDLES']}, Max: {BACKTEST_CONFIG['MAX_CANDLES']})", "INFO")
            candles_input = input("   Candles: ").strip()
            
            if not candles_input:
                num_candles = BACKTEST_CONFIG['DEFAULT_CANDLES']
            else:
                num_candles = int(candles_input)
                
            if num_candles < 100:
                print_colored("❌ Minimum 100 candles required", "ERROR")
                continue
            elif num_candles > BACKTEST_CONFIG['MAX_CANDLES']:
                print_colored(f"❌ Maximum {BACKTEST_CONFIG['MAX_CANDLES']} candles allowed", "ERROR")
                continue
                
            break
        except ValueError:
            print_colored("❌ Please enter a valid number", "ERROR")
    
    # Get currency pairs
    print_colored("\n💱 Available currency pairs:", "HEADER")
    for i, pair in enumerate(CURRENCY_PAIRS, 1):
        print_colored(f"   {i}. {pair}", "INFO")
    
    while True:
        print_colored("\nEnter pair numbers (comma-separated) or 'all' for all pairs:", "HEADER")
        pairs_input = input("   Pairs: ").strip().lower()
        
        if pairs_input == 'all':
            selected_pairs = CURRENCY_PAIRS.copy()
            break
        else:
            try:
                pair_indices = [int(x.strip()) for x in pairs_input.split(',')]
                selected_pairs = []
                
                for idx in pair_indices:
                    if 1 <= idx <= len(CURRENCY_PAIRS):
                        selected_pairs.append(CURRENCY_PAIRS[idx-1])
                    else:
                        print_colored(f"❌ Invalid pair number: {idx}", "ERROR")
                        raise ValueError
                
                if selected_pairs:
                    break
                else:
                    print_colored("❌ No valid pairs selected", "ERROR")
                    
            except ValueError:
                print_colored("❌ Please enter valid pair numbers", "ERROR")
    
    # Get strategies
    print_colored("\n🔧 Available strategies for comparison:", "HEADER")
    available_strategies = ['S1', 'S2', 'S3', 'S4']
    for i, strategy in enumerate(available_strategies, 1):
        strategy_name = STRATEGY_CONFIG[strategy]['name']
        print_colored(f"   {i}. {strategy} - {strategy_name}", "INFO")
    
    while True:
        print_colored("\nEnter strategy numbers (comma-separated) or 'all' for all strategies:", "HEADER")
        strategies_input = input("   Strategies: ").strip().lower()
        
        if strategies_input == 'all':
            selected_strategies = available_strategies.copy()
            break
        else:
            try:
                strategy_indices = [int(x.strip()) for x in strategies_input.split(',')]
                selected_strategies = []
                
                for idx in strategy_indices:
                    if 1 <= idx <= len(available_strategies):
                        selected_strategies.append(available_strategies[idx-1])
                    else:
                        print_colored(f"❌ Invalid strategy number: {idx}", "ERROR")
                        raise ValueError
                
                if selected_strategies:
                    break
                else:
                    print_colored("❌ No valid strategies selected", "ERROR")
                    
            except ValueError:
                print_colored("❌ Please enter valid strategy numbers", "ERROR")
    
    return num_candles, selected_pairs, selected_strategies

def main():
    """Main function for comparative backtesting"""
    try:
        print_colored("🔄 Comparative Trading Strategy Backtesting System", "HEADER", bold=True)
        print_colored("=" * 60, "HEADER")
        print()
        
        # Get user input
        num_candles, pairs, strategies = get_user_input()
        
        # Confirm settings
        print_header("📋 COMPARATIVE BACKTEST SETTINGS")
        print_colored(f"📈 Candles: {num_candles}", "HEADER")
        print_colored(f"💱 Pairs: {', '.join(pairs)}", "HEADER")
        print_colored(f"🔧 Strategies: {', '.join(strategies)}", "HEADER")
        print_colored(f"🔄 Comparison: Rule-based vs ML-integrated", "HEADER")
        print()
        
        confirm = input("Proceed with comparative backtest? (y/n): ").strip().lower()
        if confirm != 'y':
            print_colored("❌ Comparative backtest cancelled", "WARNING")
            return
        
        # Run comparative backtest
        comparative_system = ComparativeBacktest()
        rule_results, ml_results = comparative_system.run_comparative_backtest(pairs, strategies, num_candles)
        
        print_header("🎉 COMPARATIVE BACKTEST COMPLETED")
        print_colored("Comparative analysis has been displayed above.", "HEADER")
        
    except KeyboardInterrupt:
        print_colored("\n⚠️  Comparative backtest interrupted by user", "WARNING")
    except Exception as e:
        print_colored(f"❌ Error during comparative backtest: {str(e)}", "ERROR")

if __name__ == "__main__":
    main()
