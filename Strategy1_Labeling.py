import pandas as pd
import numpy as np

# Load the normalized dataset
df = pd.read_csv("historical_data_normalized.csv")
df.dropna(inplace=True)
df.reset_index(drop=True, inplace=True)

# Parameters
window = 50  # lookback for support/resistance
min_gap = 0.00005   # adjusted to match real forex pip range
max_gap = 0.0004

# Initialize signal column
df['strategy1_signal'] = 0

# Dynamic support/resistance levels
resistance = df['high'].rolling(window=window).max().shift(1)
support = df['low'].rolling(window=window).min().shift(1)

# Loop through candles
for i in range(window, len(df) - 1):  # exclude last candle (need next candle info)
    close = df.loc[i, 'close']
    open_ = df.loc[i, 'open']
    high = df.loc[i, 'high']
    low = df.loc[i, 'low']
    volume = df.loc[i, 'volume']
    prev_volume = df.loc[i - 1, 'volume']

    next_high = df.loc[i + 1, 'high']
    next_low = df.loc[i + 1, 'low']

    body = abs(close - open_)
    upper_wick = high - max(open_, close)
    lower_wick = min(open_, close) - low

    # --- BUY SETUP ---
    if (
        close > resistance[i] and
        open_ < resistance[i] and
        close > open_ and
        volume >= prev_volume and
        upper_wick < body * 0.3
    ):
        gap = close - next_low
        if min_gap <= abs(gap) <= max_gap:
            df.loc[i, 'strategy1_signal'] = 1
            print(f"✔ BUY SIGNAL at index {i} | Close={close}, Resistance={resistance[i]}, Next Low={next_low}, Gap={gap}")
        else:
            print(f"✘ BUY GAP out of range at index {i} | Gap={gap}")

    # --- SELL SETUP ---
    elif (
        close < support[i] and
        open_ > support[i] and
        close < open_ and
        volume >= prev_volume and
        lower_wick < body * 0.3
    ):
        gap = next_high - close
        if min_gap <= abs(gap) <= max_gap:
            df.loc[i, 'strategy1_signal'] = -1
            print(f"✔ SELL SIGNAL at index {i} | Close={close}, Support={support[i]}, Next High={next_high}, Gap={gap}")
        else:
            print(f"✘ SELL GAP out of range at index {i} | Gap={gap}")

# Save the updated DataFrame to a new CSV
df.to_csv("output_signals.csv", index=False)
print("✅ Signal generation completed and saved to 'output_signals.csv'")
