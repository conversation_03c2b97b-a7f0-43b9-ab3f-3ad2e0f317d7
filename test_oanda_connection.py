#!/usr/bin/env python3
"""
Test Oanda API Connection
"""

import requests
import json
from datetime import datetime, timedelta
import pandas as pd

# Oanda API Configuration
ACCESS_TOKEN = "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1"
ACCOUNT_ID = "101-004-********-001"
BASE_URL = "https://api-fxpractice.oanda.com"  # Practice environment

# Currency pairs to monitor
CURRENCY_PAIRS = [
    "EUR_USD", "GBP_USD", "USD_JPY", "AUD_USD", "USD_CAD",
    "AUD_CAD", "EUR_JPY", "GBP_JPY", "USD_CHF", "EUR_GBP"
]

def test_api_connection():
    """Test basic API connection"""
    print("🔗 Testing Oanda API Connection...")
    print("=" * 50)
    
    headers = {
        "Authorization": f"Bearer {ACCESS_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        # Test account access
        url = f"{BASE_URL}/v3/accounts/{ACCOUNT_ID}"
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            account_data = response.json()
            print("✅ API Connection Successful!")
            print(f"📊 Account ID: {account_data['account']['id']}")
            print(f"💰 Currency: {account_data['account']['currency']}")
            print(f"💵 Balance: {account_data['account']['balance']}")
            print(f"📈 Unrealized PL: {account_data['account']['unrealizedPL']}")
            return True
        else:
            print(f"❌ API Connection Failed!")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Connection Error: {str(e)}")
        return False

def get_live_candle_data(instrument, count=2):
    """Get live candle data for an instrument"""
    headers = {
        "Authorization": f"Bearer {ACCESS_TOKEN}",
        "Content-Type": "application/json"
    }
    
    try:
        # Get current and previous candle
        url = f"{BASE_URL}/v3/instruments/{instrument}/candles"
        params = {
            "count": count,
            "granularity": "M1",  # 1-minute candles
            "price": "MBA"  # Mid, Bid, Ask prices
        }
        
        response = requests.get(url, headers=headers, params=params)
        
        if response.status_code == 200:
            data = response.json()
            candles = data['candles']
            
            print(f"\n📊 {instrument} - Last {count} Candles:")
            print("-" * 40)
            
            for i, candle in enumerate(candles):
                time_str = candle['time'][:19].replace('T', ' ')
                mid = candle['mid']
                
                print(f"Candle {i+1}:")
                print(f"  Time: {time_str}")
                print(f"  Open: {mid['o']}")
                print(f"  High: {mid['h']}")
                print(f"  Low: {mid['l']}")
                print(f"  Close: {mid['c']}")
                print(f"  Volume: {candle['volume']}")
                print(f"  Complete: {candle['complete']}")
                print()
            
            return candles
        else:
            print(f"❌ Failed to get data for {instrument}")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error getting data for {instrument}: {str(e)}")
        return None

def test_all_pairs():
    """Test data retrieval for all currency pairs"""
    print("\n🌍 Testing All Currency Pairs...")
    print("=" * 50)
    
    successful_pairs = []
    failed_pairs = []
    
    for pair in CURRENCY_PAIRS:
        print(f"\n🔄 Testing {pair}...")
        candles = get_live_candle_data(pair, count=1)
        
        if candles:
            successful_pairs.append(pair)
            print(f"✅ {pair} - Data retrieved successfully")
        else:
            failed_pairs.append(pair)
            print(f"❌ {pair} - Failed to retrieve data")
    
    print(f"\n📊 Summary:")
    print(f"✅ Successful: {len(successful_pairs)}/{len(CURRENCY_PAIRS)} pairs")
    print(f"❌ Failed: {len(failed_pairs)}/{len(CURRENCY_PAIRS)} pairs")
    
    if successful_pairs:
        print(f"\n✅ Working pairs: {', '.join(successful_pairs)}")
    
    if failed_pairs:
        print(f"\n❌ Failed pairs: {', '.join(failed_pairs)}")
    
    return successful_pairs, failed_pairs

def get_current_time_info():
    """Get current time information"""
    now = datetime.now()
    print(f"\n🕐 Current Time Information:")
    print(f"Local Time: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"UTC Time: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Calculate seconds until next minute
    seconds_to_next_minute = 60 - now.second
    print(f"⏰ Seconds until next minute: {seconds_to_next_minute}")
    
    return seconds_to_next_minute

if __name__ == "__main__":
    print("🚀 Oanda API Connection Test")
    print("=" * 60)
    
    # Test basic connection
    if test_api_connection():
        print("\n" + "=" * 60)
        
        # Get current time info
        get_current_time_info()
        
        # Test all currency pairs
        successful_pairs, failed_pairs = test_all_pairs()
        
        print("\n" + "=" * 60)
        print("🎉 Connection Test Complete!")
        
        if len(successful_pairs) >= 8:  # At least 80% success rate
            print("✅ API is ready for live trading!")
        else:
            print("⚠️  Some pairs failed - check API limits or pair names")
    else:
        print("❌ API connection failed - check credentials")
