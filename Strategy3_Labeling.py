import pandas as pd
import numpy as np

# Load dataset
df = pd.read_csv("historical_data_normalized.csv")
df.dropna(inplace=True)
df.reset_index(drop=True, inplace=True)

# Parameters
lookback = 50            # Lookback range for S/R detection
min_rejections = 2       # At least two touches and reversals to consider S/R zone
wick_ratio_threshold = 0.4  # Wick must be ≥ 40% of body
df['strategy3_signal'] = 0  # Initialize signal column

# Function to detect reversals from previous candles
def detect_reversals(df, idx, direction):
    rejections = []
    for j in range(idx - lookback, idx - 1):
        if direction == "resistance":
            # Up then reverse down = resistance
            if df.loc[j - 1, 'close'] < df.loc[j, 'close'] and df.loc[j + 1, 'close'] < df.loc[j, 'close']:
                rejections.append(df.loc[j, 'high'])
        elif direction == "support":
            # Down then reverse up = support
            if df.loc[j - 1, 'close'] > df.loc[j, 'close'] and df.loc[j + 1, 'close'] > df.loc[j, 'close']:
                rejections.append(df.loc[j, 'low'])
    return rejections[-min_rejections:] if len(rejections) >= min_rejections else []

# Main loop
for i in range(lookback + 2, len(df) - 1):  # Start safely past lookback + 2 to access j-1, j+1
    open_ = df.loc[i, 'open']
    close = df.loc[i, 'close']
    high = df.loc[i, 'high']
    low = df.loc[i, 'low']
    volume = df.loc[i, 'volume']
    prev_volume = df.loc[i - 1, 'volume']

    body = abs(close - open_)
    upper_wick = high - max(open_, close)
    lower_wick = min(open_, close) - low

    # --------- Resistance Check (SELL Signal) ---------
    resistance_points = detect_reversals(df, i, direction="resistance")
    if resistance_points:
        resistance_zone = (min(resistance_points), max(resistance_points))
        if resistance_zone[0] <= high <= resistance_zone[1]:
            if close > open_ and upper_wick >= wick_ratio_threshold * body and volume <= prev_volume:
                df.loc[i, 'strategy3_signal'] = -1  # SELL signal (expect red next candle)
                print(f"✔ SELL Signal at index {i} | Resistance Zone: {resistance_zone}, Wick={upper_wick:.4f}, Volume={volume} <= {prev_volume}")
                continue  # Skip buy check if already sell

    # --------- Support Check (BUY Signal) ---------
    support_points = detect_reversals(df, i, direction="support")
    if support_points:
        support_zone = (min(support_points), max(support_points))
        if support_zone[0] <= low <= support_zone[1]:
            if close < open_ and lower_wick >= wick_ratio_threshold * body and volume <= prev_volume:
                df.loc[i, 'strategy3_signal'] = 1  # BUY signal (expect green next candle)
                print(f"✔ BUY Signal at index {i} | Support Zone: {support_zone}, Wick={lower_wick:.4f}, Volume={volume} <= {prev_volume}")

# Save results
df.to_csv("output_signals_S3.csv", index=False)
print("✅ Strategy 3 signals successfully saved to 'output_signals.csv'")
