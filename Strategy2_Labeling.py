import pandas as pd
import numpy as np
from scipy.stats import linregress
import os

# Load data
try:
    df = pd.read_csv("historical_data_normalized.csv")
    print("Data loaded successfully. Columns found:", df.columns.tolist())
except FileNotFoundError:
    print("ERROR: 'historical_data_normalized.csv' not found in:", os.getcwd())
    print("Files in directory:", os.listdir())
    exit()

# Check if required columns exist
required_columns = ['open', 'high', 'low', 'close', 'volume', 'rsi']
missing_cols = [col for col in required_columns if col not in df.columns]
if missing_cols:
    print(f"ERROR: Missing required columns: {missing_cols}")
    exit()
    

# Clean data
df.dropna(inplace=True)
df.reset_index(drop=True, inplace=True)

# Strategy Parameters
lookback = 70
trend_lookback = 20
min_cluster_size = 2
max_cluster_size = 4
body_size_multiplier = 1.5
breakout_multiplier = 2.0
vol_lookback = 10

# Initialize strategy columns
df['strategy2_signal'] = 0  # 1 = Buy, -1 = Sell, 0 = No Signal
df['trend'] = 0  # 1 = Uptrend, -1 = Downtrend, 0 = Neutral

# Helper Functions
def get_avg_body_size(data, idx, lookback=20):
    recent = data.loc[max(0, idx-lookback):idx]
    return np.mean(abs(recent['close'] - recent['open']))

def get_avg_volume(data, idx, lookback=10):
    recent = data.loc[max(0, idx-lookback):idx]
    return np.mean(recent['volume'])

def get_trend(data, idx, lookback=20):
    if idx < lookback:
        return 0
    prices = data.loc[idx-lookback:idx, 'close'].values
    slope = linregress(np.arange(len(prices)), prices).slope
    price_range = max(prices) - min(prices)
    if abs(slope) > (price_range * 0.005):
        return 1 if slope > 0 else -1
    return 0

def identify_order_blocks(data, idx, lookback):
    upper_blocks = []
    lower_blocks = []
    start = max(0, idx - lookback)
    avg_body_size = get_avg_body_size(data, idx, 20)
    small_body_threshold = avg_body_size * body_size_multiplier

    for cluster_size in range(min_cluster_size, max_cluster_size + 1):
        for i in range(start, idx - cluster_size - 1):
            cluster = data.loc[i:i+cluster_size-1]
            breakout_candle = data.loc[i+cluster_size]
            cluster_high = cluster['high'].max()
            cluster_low = cluster['low'].min()
            max_body = abs(cluster['close'] - cluster['open']).max()

            # Upper order block (Supply Zone)
            if (all(cluster['close'] > cluster['open']) and (max_body < small_body_threshold)):
                breakout_body = breakout_candle['open'] - breakout_candle['close']
                if breakout_body > (max_body * breakout_multiplier):
                    upper_blocks.append({
                        'start_idx': i, 'end_idx': i+cluster_size-1,
                        'low': cluster_low, 'high': cluster_high,
                        'strength': breakout_body/max_body
                    })

            # Lower order block (Demand Zone)
            if (all(cluster['close'] < cluster['open'])) and (max_body < small_body_threshold):
                breakout_body = breakout_candle['close'] - breakout_candle['open']
                if breakout_body > (max_body * breakout_multiplier):
                    lower_blocks.append({
                        'start_idx': i, 'end_idx': i+cluster_size-1,
                        'low': cluster_low, 'high': cluster_high,
                        'strength': breakout_body/max_body
                    })

    upper_blocks.sort(key=lambda x: x['strength'], reverse=True)
    lower_blocks.sort(key=lambda x: x['strength'], reverse=True)
    return upper_blocks, lower_blocks

# Main Strategy Execution
print("Running strategy on", len(df), "candles...")
for i in range(max(lookback, trend_lookback), len(df) - 1):
    current = df.loc[i]
    current_rsi = current['rsi']
    
    # Determine trend
    df.loc[i, 'trend'] = get_trend(df, i, trend_lookback)
    current_trend = df.loc[i, 'trend']
    
    # Get recent volume average
    avg_vol = get_avg_volume(df, i, vol_lookback)
    
    # Identify order blocks
    upper_blocks, lower_blocks = identify_order_blocks(df, i, lookback)
    
    # SELL Signal Conditions
    sell_signal = False
    for block in upper_blocks:
        if current_trend == 1:  # Skip in strong uptrend
            continue
            
        conditions = [
            current['high'] > block['low'],          # Wick enters block
            current['close'] < block['low'],         # Closes below block
            current['open'] < block['low'],          # Never broke into block
            current['volume'] <= avg_vol * 1.2,      # Volume not too high
            current['close'] > current['open'],      # Green candle
            current_rsi > 50                         # RSI not oversold
        ]
        
        if all(conditions):
            sell_signal = True
            break
    
    # BUY Signal Conditions
    buy_signal = False
    for block in lower_blocks:
        if current_trend == -1:  # Skip in strong downtrend
            continue
            
        conditions = [
            current['low'] < block['high'],          # Wick enters block
            current['close'] > block['high'],        # Closes above block
            current['open'] > block['high'],         # Never broke into block
            current['volume'] <= avg_vol * 1.2,      # Volume not too high
            current['close'] < current['open'],      # Red candle
            current_rsi < 50                         # RSI not overbought
        ]
        
        if all(conditions):
            buy_signal = True
            break
    
    # Assign signals
    if sell_signal:
        df.loc[i, 'strategy2_signal'] = -1
    elif buy_signal:
        df.loc[i, 'strategy2_signal'] = 1

    # Progress update
    if i % 1000 == 0:
        print(f"Processed {i}/{len(df)} candles...")

# Save results
output_file = "output_signals_S2.csv"
df.to_csv(output_file, index=False)
print(f"\nStrategy complete! Results saved to {output_file}")
print("Signal counts:")
print(df['strategy2_signal'].value_counts())