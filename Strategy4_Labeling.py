import pandas as pd
import numpy as np

# Load historical 1-minute candle data
df = pd.read_csv("historical_data_normalized.csv")
df.dropna(inplace=True)
df.reset_index(drop=True, inplace=True)

# Add signal column
df['strategy4_signal'] = 0  # 1 = BUY, -1 = SELL, 0 = no signal

# Parameters (tuned for 1-min chart)
lookback = 50
min_touch_points = 2
min_wick_ratio = 0.3  # Wick must be at least 30% of the body

def find_trendline(points):
    """Fit linear trendline to swing highs/lows"""
    x = np.array([p[0] for p in points])
    y = np.array([p[1] for p in points])
    if len(x) < 2:
        return None, None
    slope, intercept = np.polyfit(x, y, 1)
    return slope, intercept

# Main loop through candles
for i in range(lookback + 2, len(df) - 1):  # Leave room for next candle
    curr = df.loc[i]
    prev = df.loc[i - 1]

    open_, close = curr['open'], curr['close']
    high, low = curr['high'], curr['low']
    volume = curr['volume']
    prev_volume = prev['volume']
    body = abs(close - open_)

    # ------------------- BUY SETUP (Reversal after Downtrend) -------------------
    swing_highs = []
    for j in range(i - lookback, i - 1):
        if df.loc[j - 1, 'high'] < df.loc[j, 'high'] > df.loc[j + 1, 'high']:
            swing_highs.append((j, df.loc[j, 'high']))

    if len(swing_highs) >= min_touch_points:
        slope, intercept = find_trendline(swing_highs)
        if slope is not None and slope < 0:  # Confirm downtrend
            trendline_price = slope * i + intercept

            if low <= trendline_price <= high:  # Touch or wick inside
                lower_wick = min(open_, close) - low
                if close < open_ and close < trendline_price and lower_wick >= min_wick_ratio * body and volume <= prev_volume:
                    df.loc[i + 1, 'strategy4_signal'] = 1  # BUY on next candle
                    print(f"✔ BUY at {i+1} | Wick={lower_wick:.2f}, Body={body:.2f}, Volume={volume} <= {prev_volume}")

    # ------------------- SELL SETUP (Reversal after Uptrend) -------------------
    swing_lows = []
    for j in range(i - lookback, i - 1):
        if df.loc[j - 1, 'low'] > df.loc[j, 'low'] < df.loc[j + 1, 'low']:
            swing_lows.append((j, df.loc[j, 'low']))

    if len(swing_lows) >= min_touch_points:
        slope, intercept = find_trendline(swing_lows)
        if slope is not None and slope > 0:  # Confirm uptrend
            trendline_price = slope * i + intercept

            if low <= trendline_price <= high:  # Touch or wick inside
                upper_wick = high - max(open_, close)
                if close > open_ and close > trendline_price and upper_wick >= min_wick_ratio * body and volume <= prev_volume:
                    df.loc[i + 1, 'strategy4_signal'] = -1  # SELL on next candle
                    print(f"✔ SELL at {i+1} | Wick={upper_wick:.2f}, Body={body:.2f}, Volume={volume} <= {prev_volume}")

# Save signals
df.to_csv("output_signals_S4.csv", index=False)
print("✅ Strategy 4 signals saved in 'output_signals_S4.csv'")
